import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Only return non-sensitive default values
    // API keys and model names should not be exposed to frontend
    const config = {
      apiKey: "", // Never expose API keys to frontend
      apiBase: process.env.OPENAI_API_BASE || "https://api.openai.com/v1",
      modelName: "", // Never expose model names to frontend
      hasEnvConfig: !!(process.env.OPENAI_API_KEY && process.env.MODEL_NAME), // Just indicate if env config exists
    };

    return NextResponse.json(config);
  } catch (error) {
    console.error("Error reading config:", error);
    return NextResponse.json({
      apiKey: "",
      apiBase: "https://api.openai.com/v1",
      modelName: "",
      hasEnvConfig: false,
    });
  }
}
