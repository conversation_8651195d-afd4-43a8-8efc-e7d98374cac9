import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Return environment variables that can be used as defaults
    // These are safe to expose as they are just default values
    const config = {
      apiKey: process.env.OPENAI_API_KEY || "",
      apiBase: process.env.OPENAI_API_BASE || "https://api.openai.com/v1",
      modelName: process.env.MODEL_NAME || "",
    };

    return NextResponse.json(config);
  } catch (error) {
    console.error("Error reading config:", error);
    return NextResponse.json(
      { 
        apiKey: "",
        apiBase: "https://api.openai.com/v1",
        modelName: "",
      }
    );
  }
}
